% 离散粒子群算法计算最优压降
clc
clear
close all

N = 11;                                 %可以选择的节点总数
%考虑管长
LLL=[1000,3000;1300,2000;1500,800;2000,240;700,3500;1400,3000;1900,2000;2500,1000;2000,4000;3500,3500;4000,1800];
LL1=zeros(50,1);
LL2=zeros(50,1);
%考虑管长
%% 粒子群算法相关参数
np = 50;                                % 种群个数
gen = 50;                               % 迭代次数
dim = 2;                                % 决策变量的维度
c1 = 2;                                 % 学习因子
c2 = 2;
w_max = 0.8;                            % 惯性权重
w_min = 0.4;
v_max = 0.3*N;                          % 粒子的速度限制
v_min = -v_max;

%% 初始化粒子集合和速度
for i=1:np
    x(i,:) = randperm(N,dim);
    LL1(i,1)=LLL(x(i,1),1);%考虑管长
    LL2(i,1)=LLL(x(i,2),2);%考虑管长
end
v = v_min + rand(np,dim)*(v_max-v_min);
    
%% 计算初始化 个体最优 和 全局最优
% 个体最优
pbest = x;
for i=1:np
    pbest_fit(i,1) = myfun(x(i,:),LL1(i,1),LL2(i,1));       %存储个体最优值
end
% 全局最优
[global_best_fit,idx] = min(pbest_fit);
global_best = pbest(idx(1),:);

%% 迭代计算
for t=1:gen
    % 计算动态权重系数
    w = (w_max - w_max-w_min)*t/gen;
    for i=1:np
        % 更新粒子的速度
        v(i,:) = w * v(i,:) + c1*rand*(pbest(i,:)-x(i, :)) + c2*rand*(global_best-x(i,:));
        % 对速度超出边界进行限制
        v(i,:) = max(min(v(i,:), v_max), v_min);
        
        % 更新粒子的位置，并确保为整数
        x(i,:) = x(i,:) + round(v(i,:));
        
        % 检查位置边界
        x(i,:) = max(min(x(i,:), N), 1);
        
        % 防止重复：如果位置有重复值，重新生成
        while length(x(i,:)) ~= length(unique(x(i,:)))
            x(i,:) = randperm(N, dim);
        end
    end
    %计算个体历史最优和全局最优
    for i=1:np
        fitness = myfun(x(i,:), LL1(i,1), LL2(i,1));%考虑管长
        if fitness<pbest_fit(i)
            pbest_fit(i) = fitness;
            pbest(i,:) = x(i,:);
        end
        if fitness<global_best_fit
            global_best_fit = fitness;
            global_best = x(i,:);
        end
    end
    Convergence_curve(t) = global_best_fit;      % 记录每次迭代中
    disp(['第',num2str(t),'代计算完成。','最优选点为',num2str(global_best),'。适应度值为',num2str(global_best_fit)])
end

% 绘制收敛曲线
figure,
plot(Convergence_curve);


