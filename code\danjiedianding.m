clear
close all

% --- 0. 数据加载 ---
disp('--- 开始加载数据 ---');
data1 = xlsread('许仕荣87页管网.xlsx','管网基本参数');
data2_orig = xlsread('许仕荣87页管网.xlsx','管段数据'); % 保存原始管段数据
data3 = xlsread('许仕荣87页管网.xlsx','节点流量');
data4 = xlsread('许仕荣87页管网.xlsx','水源数据');
disp('数据加载成功。');

% --- 提取基本参数 (在循环外定义，因为它们不变) ---
N=data1(1);             % 管段数
MT=data1(2);            % 计入水源后总节点数
L=data1(3);             % 实环数 (未使用)
LX=data1(4);            % 虚实环数
N_tower=data1(5);       % 水塔数
N_pump=data1(6);        % 泵站数
N_source=N_tower+N_pump;% 水源数
M=MT-N_source;          % 不含水源的节点数
C_coeff=data1(7);       % 水头参数 (C 与 MATLAB 函数名冲突，改名)
m_exp=data2_orig(1,8);  % 摩阻指数 (假设全局唯一)

% --- 如果LX~=0，读取相关数据 (在循环外定义) ---
if LX~=0
    QX=data3(:,2);      % QX,节点流量m^3/s
    HX=data4(:,2);      % 水源总水头
    SX=data4(:,3);      % 水源出水管虚阻耗系数(s^2/(m^3))
    NQ_pump_pipes=data4(:,4); % 水源出水管管段编号 (泵管)
else
    QX=zeros(M,1);
    HX=zeros(N_source,1);
    SX=ones(N_source,1);
    NQ_pump_pipes=zeros(N_source,1);
end

EZ=0.05;                % 回路水头闭合容差 (未使用)
STR=0.5;                % 松弛因子

% --- 参考流量与压头 (用于最终校验) ---
Q_ref = [
    0.4164; 0.1044; 0.0149; -0.1854; 0.3179; 0.2767; 0.0530; 0.1188; 0.0962; 0.1483;
    0.0525; -0.0398; 0.0800; 0.0679; 0.0144; 0.0093; 0.0299; 0.0548; -0.0358; 0.0811;
    0.4213; 0.3208
];
E_ref = [
    74.576428; 72.573442; 72.167468; 75.159936; 73.109308; 70.810565; 67.205452;
    72.065420; 70.142379; 68.351104; 64.834299; 73.496291; 86.845284; 76.950363;
    92.000000; 89.000000; 74.000000
];

% --- 探索参数定义 ---
pipe5_length_min = 1000;
pipe5_length_max = 2500; 
pipe5_length_step = 1;

target_sum_Hs = 49;
sum_Hs_tolerance = 20;
flow_deviation_percent = 15;
head_deviation_percent = 10;

max_hydraulic_iterations = 200; 
convergence_threshold_EQ = 1e-4;

% --- 结果存储 ---
suitable_solutions = [];
best_solution.pipe5_length = NaN;
best_solution.sum_Hs = NaN;
best_solution.metric_sum_Hs_diff = Inf;
best_solution.Q_calculated = [];
best_solution.E_calculated = [];
best_solution.converged = false;

% --- 新增：用于追踪所有测试管长中最低的总压降 ---
min_overall_calculated_sum_Hs = Inf;
pipe_L_for_min_overall_sum_Hs = NaN;
% --- 新增结束 ---

disp(['--- 开始搜索泵2管长 (data2(5,6)) 从 ', num2str(pipe5_length_min), 'm 到 ', num2str(pipe5_length_max), 'm ---']);
disp(['--- 水力计算最大迭代次数设置为: ', num2str(max_hydraulic_iterations), ' ---']);

% --- 外层循环：改变 data2(5,6) 的管长 ---
for current_pipe5_L = pipe5_length_min : pipe5_length_step : pipe5_length_max
    fprintf('--- 正在测试管长 data2(5,6) = %.2f m ---\n', current_pipe5_L);

    data2 = data2_orig; % 每次迭代都从原始数据开始

    % --- 设置当前迭代的管网参数 ---
    data2(1,3) = 5;     % 泵站1 连接到节点1
    data2(5,3) = 1;     % 泵站2 连接到节点3
    data2(1,6) = current_pipe5_L;  % 泵站1的管长
    data2(5,6) = 300; % 当前测试的泵站2的管长

    % --- 基于 data2 更新相关参数 ---
    NM = data2(:,2:3);
    LD = data2(:,6);
    D  = data2(:,7);
    Q_initial  = data2(:,9);

    % --- 计算当前管段流量系数 R ---
    R_coeffs = zeros(N,1);
    for i_R=1:N
        if LD(i_R) <= 0
            R_coeffs(i_R) = 0;
        else
            R_coeffs(i_R) = 0.27853 * C_coeff * D(i_R)^2.63 / LD(i_R)^0.54;
        end
    end
    if N_pump ~= 0 && LX ~= 0
        for i_p_pump=1:N_pump
             pump_pipe_idx = NQ_pump_pipes(i_p_pump);
             if pump_pipe_idx > 0 && pump_pipe_idx <= N
                 if SX(i_p_pump) <= 0
                     R_coeffs(pump_pipe_idx) = Inf;
                 else
                    R_coeffs(pump_pipe_idx) = 1 / SX(i_p_pump)^0.5;
                 end
             end
        end
    end

    % --- 节点初始压头 ---
    E_calc_full = zeros(MT,1);
    E_calc_full(1:M) = 70;
    if N_source > 0 && LX ~=0
        E_calc_full(M+1:MT) = HX(1:N_source);
    elseif N_source > 0 && LX == 0
        disp('警告: 有水源但LX=0，水源压力HX未从Excel加载，可能导致计算不准确。');
    end

    % --- 生成MIS,MJS矩阵 ---
    MIS = zeros(M, N);
    MJS = zeros(M, N);
    MM_connected_pipes_count = zeros(M,1);
    for I_node=1:M
        K_conn_count=0;
        for J_pipe=1:N
            if NM(J_pipe,1)==I_node
                K_conn_count=K_conn_count+1;
                MIS(I_node,K_conn_count)=J_pipe;
                MJS(I_node,K_conn_count)=NM(J_pipe,2);
            end
            if NM(J_pipe,2)==I_node
                K_conn_count=K_conn_count+1;
                MIS(I_node,K_conn_count)=-J_pipe;
                MJS(I_node,K_conn_count)=NM(J_pipe,1);
            end
        end
        MM_connected_pipes_count(I_node)=K_conn_count;
    end

    Q_calculated = Q_initial;

    % --- 水力计算迭代 (KK循环) ---
    converged_hydraulics = false;
    EQ_max_history = []; 

    for KK=1:max_hydraulic_iterations
        % (1) 计算各管段流量 Q_calculated
        for I_p=1:N
            node1 = NM(I_p,1);
            node2 = NM(I_p,2);
            ET_head_diff = E_calc_full(node1) - E_calc_full(node2);
            is_pump = false;
            if N_pump ~= 0 && LX ~= 0
                for idx_pump_check = 1:N_pump
                    if NQ_pump_pipes(idx_pump_check) == I_p
                        if abs(ET_head_diff) < 1e-7
                            Q_calculated(I_p) = 0;
                        else
                            Q_calculated(I_p) = R_coeffs(I_p) * abs(ET_head_diff)^0.5 * sign(ET_head_diff);
                        end
                        is_pump = true;
                        break;
                    end
                end
            end
            if ~is_pump
                if R_coeffs(I_p) == 0
                    Q_calculated(I_p) = 0;
                elseif abs(ET_head_diff) < 1e-7
                    Q_calculated(I_p) = 0;
                else
                    Q_calculated(I_p) = R_coeffs(I_p) * abs(ET_head_diff)^0.54 * sign(ET_head_diff);
                end
            end
        end

        % (2) 计算节点流量不平衡量 EQ
        EQ_imbalance = zeros(M,1);
        if LX~=0
            for I_n=1:M
              EQ_imbalance(I_n)=QX(I_n);
              for J_conn=1:MM_connected_pipes_count(I_n)
                  pipe_abs_idx = abs(MIS(I_n,J_conn));
                  pipe_sign_in_MIS = sign(MIS(I_n,J_conn));
                  EQ_imbalance(I_n) = EQ_imbalance(I_n) + Q_calculated(pipe_abs_idx) * pipe_sign_in_MIS;
              end
            end
        end

        current_EQ_max = 0;
        if LX~=0
            current_EQ_max = max(abs(EQ_imbalance));
            EQ_max_history = [EQ_max_history, current_EQ_max];
            if current_EQ_max < convergence_threshold_EQ && KK > 1
                converged_hydraulics = true;
                break;
            end
        elseif LX==0 && KK >= 10
             converged_hydraulics = true;
             break;
        end

        % (3) 生成系数矩阵 AJ
        QJ_derivatives = zeros(N,1);
        for I_p=1:N
            node1 = NM(I_p,1);
            node2 = NM(I_p,2);
            ET_head_diff = E_calc_full(node1) - E_calc_full(node2);
            if abs(ET_head_diff) < 1e-7
                ET_for_deriv = 1e-7 * sign(ET_head_diff);
                if ET_for_deriv == 0, ET_for_deriv = 1e-7; end
            else
                ET_for_deriv = ET_head_diff;
            end
            is_pump_deriv = false;
            if N_pump ~= 0 && LX ~= 0
                 for idx_pump_check_deriv = 1:N_pump
                    if NQ_pump_pipes(idx_pump_check_deriv) == I_p
                        if R_coeffs(I_p) == 0 || abs(ET_for_deriv) < 1e-9
                            QJ_derivatives(I_p) = 1e9;
                        else
                            QJ_derivatives(I_p) = R_coeffs(I_p) * 0.5 * abs(ET_for_deriv)^(-0.5);
                        end
                        is_pump_deriv = true;
                        break;
                    end
                 end
            end
            if ~is_pump_deriv
                if R_coeffs(I_p) == 0 || abs(ET_for_deriv) < 1e-9
                     QJ_derivatives(I_p) = 1e9;
                else
                    QJ_derivatives(I_p) = R_coeffs(I_p) * 0.54 * abs(ET_for_deriv)^(-0.46);
                end
            end
        end

        AJ_jacobian = zeros(M,M);
        for I_n=1:M
          for J_conn=1:MM_connected_pipes_count(I_n)
              pipe_abs_idx = abs(MIS(I_n,J_conn));
              other_node_idx = MJS(I_n,J_conn);
              AJ_jacobian(I_n,I_n) = AJ_jacobian(I_n,I_n) + QJ_derivatives(pipe_abs_idx);
              if other_node_idx <= M
                AJ_jacobian(I_n,other_node_idx) = AJ_jacobian(I_n,other_node_idx) - QJ_derivatives(pipe_abs_idx);
              end
          end
          if AJ_jacobian(I_n,I_n) == 0
              AJ_jacobian(I_n,I_n)=1e11;
          end
        end

        % (4) 解线性方程组，更新节点压头 E_calc_full
        DE_head_correction = zeros(M,1);
        if LX~=0
            if rcond(AJ_jacobian) < 1e-15
                DE_head_correction = zeros(M,1);
            else
                DE_head_correction = AJ_jacobian \ EQ_imbalance;
            end
        end
        for I_n=1:M
            E_calc_full(I_n) = E_calc_full(I_n) - STR * DE_head_correction(I_n);
        end

        if KK == max_hydraulic_iterations && ~converged_hydraulics && LX~=0
             % disp(['  警告: 达到最大迭代次数 ', num2str(max_hydraulic_iterations), ...
             %      '，水力计算未收束。最大流量不平衡: ', num2str(current_EQ_max)]);
        end
    end % KK循环结束 (水力计算迭代)

    if ~converged_hydraulics && LX~=0
       fprintf('  注意: 管长 %.2f m 时水力计算未在 %d 次迭代内收束到阈值 %.1e。最后不平衡量: %.2e\n', ...
               current_pipe5_L, max_hydraulic_iterations, convergence_threshold_EQ, current_EQ_max);
    end

    % --- (3) 管段压降和条件 ---
    H_collect.H1=E_calc_full(13)-E_calc_full(1); H_collect.H2=E_calc_full(1)-E_calc_full(2);
    H_collect.H3=E_calc_full(2)-E_calc_full(3); H_collect.H4=E_calc_full(3)-E_calc_full(4);
    H_collect.H5=E_calc_full(14)-E_calc_full(4); H_collect.H6=E_calc_full(1)-E_calc_full(5);
    H_collect.H7=E_calc_full(2)-E_calc_full(6); H_collect.H8=E_calc_full(3)-E_calc_full(7);
    H_collect.H9=E_calc_full(4)-E_calc_full(8); H_collect.H10=E_calc_full(5)-E_calc_full(6);
    H_collect.H11=E_calc_full(6)-E_calc_full(7); H_collect.H12=E_calc_full(7)-E_calc_full(8);
    H_collect.H13=E_calc_full(5)-E_calc_full(9); H_collect.H14=E_calc_full(6)-E_calc_full(10);
    H_collect.H15=E_calc_full(7)-E_calc_full(11); H_collect.H16=E_calc_full(12)-E_calc_full(8);
    H_collect.H17=E_calc_full(9)-E_calc_full(10); H_collect.H18=E_calc_full(10)-E_calc_full(11);
    H_collect.H19=E_calc_full(11)-E_calc_full(12); H_collect.H20=E_calc_full(17)-E_calc_full(12);
    H_collect.H21=E_calc_full(15)-E_calc_full(13); H_collect.H22=E_calc_full(16)-E_calc_full(14);

    calculated_sum_Hs = H_collect.H1 + H_collect.H5 + H_collect.H2 + H_collect.H3 + H_collect.H4 + H_collect.H10 + ...
                        H_collect.H6 + H_collect.H7 + H_collect.H8 + H_collect.H9 + ...
                        H_collect.H11 + H_collect.H12 + H_collect.H13 + H_collect.H14 + ...
                        H_collect.H15 + H_collect.H16 + H_collect.H17 + H_collect.H18 + ...
                        H_collect.H19 + H_collect.H20 + H_collect.H21 + H_collect.H22;

    fprintf('    当前管长 %.2f m, 计算得到的 sum_of_specified_Hs = %.4f\n', current_pipe5_L, calculated_sum_Hs);

    % --- 新增：更新所有测试管长中最低的总压降记录 ---
    if calculated_sum_Hs < min_overall_calculated_sum_Hs
        min_overall_calculated_sum_Hs = calculated_sum_Hs;
        pipe_L_for_min_overall_sum_Hs = current_pipe5_L;
    end
    % --- 新增结束 ---

    % --- 条件校验 ---
    % (1) 流量条件
    flow_ok = true;
    if isempty(Q_ref) || length(Q_calculated) ~= length(Q_ref)
        flow_ok = false;
    else
        for i_q=1:N
            if abs(Q_ref(i_q)) < 1e-6
                if abs(Q_calculated(i_q) - Q_ref(i_q)) > 1e-4
                    flow_ok = false; break;
                end
            else
                if abs(Q_calculated(i_q) - Q_ref(i_q)) / abs(Q_ref(i_q)) * 100 > flow_deviation_percent
                    flow_ok = false; break;
                end
            end
        end
    end

    % (2) 节点压力条件
    pressure_ok = true;
    if isempty(E_ref) || length(E_calc_full) ~= length(E_ref)
        pressure_ok = false;
    else
        for i_e=1:MT 
             if abs(E_ref(i_e)) < 1e-3
                if abs(E_calc_full(i_e) - E_ref(i_e)) > 0.5 
                    pressure_ok = false; break;
                end
            else
                if abs(E_calc_full(i_e) - E_ref(i_e)) / abs(E_ref(i_e)) * 100 > head_deviation_percent
                    pressure_ok = false; break;
                end
            end
        end
    end

    sum_Hs_ok = abs(calculated_sum_Hs - target_sum_Hs) <= sum_Hs_tolerance;

    % --- 记录满足条件的解 ---
    current_conditions_met = converged_hydraulics && flow_ok && pressure_ok && sum_Hs_ok;

    if current_conditions_met
        fprintf('  >>> 找到满足所有条件的解: 管长 %.2f m, sum_Hs = %.4f (目标 %.1f)\n', ...
                current_pipe5_L, calculated_sum_Hs, target_sum_Hs);

        entry.pipe5_length = current_pipe5_L;
        entry.sum_Hs = calculated_sum_Hs;
        entry.Q_calculated = Q_calculated;
        entry.E_calculated = E_calc_full;
        entry.converged = converged_hydraulics;
        if LX~=0, entry.max_EQ_imbalance = current_EQ_max; else, entry.max_EQ_imbalance = NaN; end
        suitable_solutions = [suitable_solutions; entry];

        current_sum_Hs_diff = abs(calculated_sum_Hs - target_sum_Hs);
        if current_sum_Hs_diff < best_solution.metric_sum_Hs_diff
            best_solution.metric_sum_Hs_diff = current_sum_Hs_diff;
            best_solution.pipe5_length = current_pipe5_L;
            best_solution.sum_Hs = calculated_sum_Hs;
            best_solution.Q_calculated = Q_calculated;
            best_solution.E_calculated = E_calc_full;
            best_solution.converged = converged_hydraulics;
            if LX~=0, best_solution.max_EQ_imbalance = current_EQ_max; else, best_solution.max_EQ_imbalance = NaN; end
        end
    end

end % current_pipe5_L 循环结束

% --- 显示最终结果 ---
disp('----------------------------------------------------------');
% --- 新增：显示搜索到的最低总压降 ---
if ~isnan(pipe_L_for_min_overall_sum_Hs)
    fprintf('在所有测试的管长中，找到的最低管网总压降 (sum_of_specified_Hs) 是: %.4f\n', min_overall_calculated_sum_Hs);
    fprintf('  该最低总压降对应的泵2管长 (data2(5,6)) 是: %.2f m\n', pipe_L_for_min_overall_sum_Hs);
else
    disp('未能记录到任何管长的总压降数据 (可能循环未执行或calculated_sum_Hs始终为NaN/Inf)。');
end
disp('----------------------------------------------------------');
% --- 新增结束 ---

if ~isempty(suitable_solutions)
    fprintf('共找到 %d 个满足所有条件的泵2管长方案。\n', size(suitable_solutions,1));
    if ~isnan(best_solution.pipe5_length)
        disp('其中，sum_of_specified_Hs 最接近目标值 49 的方案是:');
        fprintf('  最佳泵2管长 (data2(5,6)): %.2f m\n', best_solution.pipe5_length);
        fprintf('  对应的 sum_of_specified_Hs: %.4f (与目标 %.1f 的差的绝对值为: %.4f)\n', ...
                best_solution.sum_Hs, target_sum_Hs, best_solution.metric_sum_Hs_diff);
        if LX ~=0
            fprintf('  水力计算收束状态: %s (最后最大节点流量不平衡量: %.2e)\n', mat2str(best_solution.converged), best_solution.max_EQ_imbalance);
        else
            fprintf('  水力计算完成状态 (LX=0): %s\n', mat2str(best_solution.converged));
        end

        disp('  该方案下的计算管段流量 Q_calculated:');
        disp(best_solution.Q_calculated');
        disp('  该方案下的计算节点压力 E_calculated (共 %d 个节点):');
        disp(best_solution.E_calculated');
    else
        disp('找到了满足条件的方案，但未能确定最佳方案（可能所有方案的sum_Hs差值相同或初始值未被覆盖）。');
    end
else
    disp(['在指定的管长范围内 (',num2str(pipe5_length_min), 'm-', num2str(pipe5_length_max),'m)，未找到满足所有条件的泵2管长。']);
    disp('您可以尝试调整以下参数再进行搜索:');
    disp('  - data2(5,6) 的搜索范围或步长');
    disp('  - 流量/压力的允许偏差百分比');
    disp(['  - sum_Hs 的目标值 (',num2str(target_sum_Hs), ') 或其允许误差 (', num2str(sum_Hs_tolerance),')']);
    disp(['  - 水力计算的迭代次数 (当前 ',num2str(max_hydraulic_iterations),') 或收敛阈值']);
end
disp('----------------------------------------------------------');
