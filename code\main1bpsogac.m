
% 主优化脚本：结合PSO和GA优化泵站接入点
clc;          % 清除命令行窗口
clear;        % 清除工作空间中的所有变量
close all;    % 关闭所有打开的图形窗口

% --- 1. 数据加载与初始化 ---
disp('开始加载数据...');
% 从Excel文件加载管网数据 (假设文件与脚本在同一目录下)
% data1: 管网基本参数
% data2: 管段数据
% data3: 节点流量数据
% data4: 水源数据
try
    data1 = xlsread('许仕荣87页管网.xlsx', '管网基本参数');
    data2 = xlsread('许仕荣87页管网.xlsx', '管段数据');
    data3 = xlsread('许仕荣87页管网.xlsx', '节点流量');
    data4 = xlsread('许仕荣87页管网.xlsx', '水源数据');
    disp('数据加载成功。');
catch ME
    disp('错误：无法从Excel文件加载数据。请检查文件名和工作表名是否正确，以及文件是否存在。');
    disp(ME.message);
    return; % 数据加载失败则退出
end

% 定义泵站1和泵站2允许接入的节点集合
% X(1) (泵站1) 可选择的点: 1, 5, 6, 9, 10
% X(2) (泵站2) 可选择的点: 2, 3, 4, 7
allowed_nodes_pump1 = [1, 5, 6, 9, 10];
allowed_nodes_pump2 = [ 2, 3, 4, 7];
N1_allowed = length(allowed_nodes_pump1); % 泵站1允许的节点数
N2_allowed = length(allowed_nodes_pump2); % 泵站2允许的节点数

% 定义参考数据 (原始流量和原始节点压头，用于myfun1b中的约束判断)
% 原始管段流量 Q1 (共N个管段, N由data1(1)给出)
% 注意：这里的 Q1 长度需要与 data1(1) 中定义的管段数 N 一致
% 您在问题中提供的 Q1 有22个值
Q1 = [
    0.4164; 0.1044; 0.0149; -0.1854; 0.3179; 0.2767; 0.0530; 0.1188; 0.0962; 0.1483;
    0.0525; -0.0398; 0.0800; 0.0679; 0.0144; 0.0093; 0.0299; 0.0548; -0.0358; 0.0811;
    0.4213; 0.3208
];
if size(Q1,1) ~= data1(1)
    warning('提供的Q1长度 (%d) 与data1中管段数 (%d) 不符，请检查。', size(Q1,1), data1(1));
    % 可以选择在此处停止或调整
end


% 原始节点压头 E1 (共MT个节点, MT由data1(2)给出)
% 您在问题中提供的 E1 有17个值
E1 = [
    74.576428; 72.573442; 72.167468; 75.159936; 73.109308; 70.810565; 67.205452;
    72.065420; 70.142379; 68.351104; 64.834299; 73.496291; 86.845284; 76.950363;
    92.000000; 89.000000; 74.000000
];
if size(E1,1) ~= data1(2)
    warning('提供的E1长度 (%d) 与data1中总节点数 (%d) 不符，请检查。', size(E1,1), data1(2));
    % 可以选择在此处停止或调整
end

disp('参考流量和压头数据已定义。');

% --- 2. PSO算法相关参数定义 ---
disp('设置PSO和GA参数...');
np = 50;         % 种群数量 (粒子数量)
gen = 100;       % 最大迭代次数
dim = 2;         % 决策变量的维度 (两个泵站的接入点)
c1 = 2;          % 学习因子1 (认知部分)
c2 = 2;          % 学习因子2 (社会部分)
w_max = 0.9;     % 最大惯性权重
w_min = 0.4;     % 最小惯性权重 % (原为0.1，可根据情况调整)

% 速度限制：这里速度代表在允许节点列表中的索引变化量
% 例如，如果允许节点有4个，速度为1表示移动到下一个节点
v_max_abs = 1;   % 每次迭代时，在允许节点列表中的最大索引变化量 (取整后)
v_min_abs = -1;  % 最小索引变化量

mutation_prob_ga = 0.1; % GA的变异概率

% --- 3. 初始化粒子群 (位置和速度) ---
disp('初始化粒子群...');
x = zeros(np, dim); % 存储每个粒子的位置 (接入点组合)
v = zeros(np, dim); % 存储每个粒子的速度

for i = 1:np
    % 初始化位置：从各自允许的节点集合中随机选择
    x(i,1) = allowed_nodes_pump1(randi(N1_allowed));
    x(i,2) = allowed_nodes_pump2(randi(N2_allowed));

    % 初始化速度：在限制范围内随机生成
    v(i,:) = rand(1, dim) * (v_max_abs - v_min_abs) + v_min_abs; % 速度可以是小数
end
disp('粒子群初始化完成。');

% --- 4. 计算初始适应度，并初始化个体最优和全局最优 ---
disp('计算初始适应度...');
pbest = x;                      % 个体最优位置初始化为初始位置
pbest_fit = zeros(np, 1);       % 存储个体最优适应度值
global_best_fit = inf;          % 全局最优适应度值初始化为无穷大
global_best = zeros(1, dim);    % 存储全局最优位置

for i = 1:np
    % 调用myfun1b计算适应度，传递所有必需的数据，包括Q1和E1
    pbest_fit(i) = myfun1b(x(i,:), data1, data2, data3, data4, Q1, E1);
    if pbest_fit(i) < global_best_fit
        global_best_fit = pbest_fit(i);
        global_best = x(i,:);
    end
end
disp(['初始全局最优适应度: ', num2str(global_best_fit)]);
disp(['初始全局最优选点: 泵1接Node ', num2str(global_best(1)), ', 泵2接Node ', num2str(global_best(2))]);

% --- 5. PSO-GA 主循环 ---
disp('开始PSO-GA迭代优化...');
Convergence_curve = zeros(gen, 1); % 记录每次迭代的全局最优适应度值

for t = 1:gen
    % 计算动态惯性权重 w (线性递减)
    w = w_max - (w_max - w_min) * (t / gen);

    % -- PSO阶段 --
    for i = 1:np
        % 更新速度 v(i,:)
        v(i,:) = w * v(i,:) + ...
                 c1 * rand(1, dim) .* (pbest(i,:) - x(i,:)) + ... % 认知部分
                 c2 * rand(1, dim) .* (global_best - x(i,:));    % 社会部分

        % 限制速度
        v(i,:) = max(min(v(i,:), v_max_abs), v_min_abs); % 这里用绝对值限制变化量
                                                       % 如果v_max_abs等定义为标量，rand(1,dim)应为rand()

        % 更新位置 x(i,:) 并确保其在允许的离散节点集合内
        % 对泵1的接入点 x(i,1)
        current_idx1 = find(allowed_nodes_pump1 == x(i,1), 1);
        if isempty(current_idx1) % 安全检查，理论上x(i,1)应始终有效
            current_idx1 = randi(N1_allowed);
        end
        % 速度影响索引的变化，round确保是整数步长
        new_idx1 = current_idx1 + round(v(i,1));
        % 确保新索引在允许范围内 (周期性边界条件)
        new_idx1 = mod(new_idx1 - 1, N1_allowed) + 1;
        x(i,1) = allowed_nodes_pump1(new_idx1);

        % 对泵2的接入点 x(i,2)
        current_idx2 = find(allowed_nodes_pump2 == x(i,2), 1);
        if isempty(current_idx2)
            current_idx2 = randi(N2_allowed);
        end
        new_idx2 = current_idx2 + round(v(i,2));
        new_idx2 = mod(new_idx2 - 1, N2_allowed) + 1;
        x(i,2) = allowed_nodes_pump2(new_idx2);
    end

    % -- 适应度评估与最优更新阶段 --
    for i = 1:np
        % 计算新位置的适应度
        current_fitness = myfun1b(x(i,:), data1, data2, data3, data4, Q1, E1);

        % 更新个体最优
        if current_fitness < pbest_fit(i)
            pbest_fit(i) = current_fitness;
            pbest(i,:) = x(i,:);
        end

        % 更新全局最优
        if current_fitness < global_best_fit
            global_best_fit = current_fitness;
            global_best = x(i,:);
        end
    end

    % -- GA阶段 (基于pbest进行种群再生) --
    % 1. 选择 (锦标赛选择法从pbest中选择父代)
    %    这里简化处理：将pbest直接作为父代进行交叉和变异，生成新的x种群
    parents_for_ga = pbest; % 使用当前的个体最优集合作为父代

    % 2. 交叉 (简单单点交叉)
    offspring_crossed = parents_for_ga; % 初始化子代
    for k = 1:2:np % 每两个父代进行交叉
        if k+1 <= np
            if rand < 0.7 % 假设交叉概率为0.7
                crossover_point = randi(dim -1); % 对于dim=2, 交叉点只能是1
                                                 % 即交换第一个元素或不交换(如果交叉点在末尾)
                                                 % 或者更简单：随机决定是否交换每个维度
                if rand < 0.5 % 交换第一个泵的节点
                    temp_node = offspring_crossed(k,1);
                    offspring_crossed(k,1) = offspring_crossed(k+1,1);
                    offspring_crossed(k+1,1) = temp_node;
                end
                if rand < 0.5 % 交换第二个泵的节点
                    temp_node = offspring_crossed(k,2);
                    offspring_crossed(k,2) = offspring_crossed(k+1,2);
                    offspring_crossed(k+1,2) = temp_node;
                end
            end
        end
    end

    % 3. 变异
    offspring_mutated = offspring_crossed;
    for k = 1:np
        if rand < mutation_prob_ga % 对每个个体以一定概率进行变异
            dim_to_mutate = randi(dim); % 选择要变异的维度 (泵1或泵2)
            if dim_to_mutate == 1
                offspring_mutated(k,1) = allowed_nodes_pump1(randi(N1_allowed));
            else
                offspring_mutated(k,2) = allowed_nodes_pump2(randi(N2_allowed));
            end
        end
    end

    % 用GA产生的子代替换当前种群x，作为下一次PSO迭代的起点
    x = offspring_mutated;

    % 记录当前迭代的全局最优适应度
    Convergence_curve(t) = global_best_fit;

    % 显示当前迭代信息
    disp(['第 ', num2str(t), ' 代完成。全局最优适应度: ', num2str(global_best_fit), ...
          ', 最优选点: 泵1->Node ', num2str(global_best(1)), ...
          ', 泵2->Node ', num2str(global_best(2))]);
end
disp('优化完成。');

% --- 6. 结果展示 ---
disp('-----------------------------------------');
disp('最终优化结果:');
disp(['全局最优适应度值 (目标函数值): ', num2str(global_best_fit)]);
disp(['最优泵站接入点组合: 泵站1 -> 节点 ', num2str(global_best(1)), ...
      ', 泵站2 -> 节点 ', num2str(global_best(2))]);

figure;
plot(1:gen, Convergence_curve, '-b', 'LineWidth', 1.5);
title('PSO-GA 优化收敛曲线', 'FontSize', 14);
xlabel('迭代次数', 'FontSize', 12);
ylabel('全局最优适应度值', 'FontSize', 12);
grid on;
legend('全局最优适应度');
disp('收敛曲线已绘制。');

