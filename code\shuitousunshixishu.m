clc;
clear;
close all;

% 读取 Excel 文件中的数据
data1 = xlsread('许仕荣87页管网.xlsx', '管网基本参数');
data2_original = xlsread('许仕荣87页管网.xlsx', '管段数据'); % 保留原始管段数据
data3 = xlsread('许仕荣87页管网.xlsx', '节点流量');
data4 = xlsread('许仕荣87页管网.xlsx', '水源数据');

% 管网基本参数赋值
N = data1(1);       % 管段数
MT = data1(2);      % 计入水源后总节点数
L = data1(3);       % 实环数
LX = data1(4);      % 虚实环数
N_tower_count = data1(5); % 水塔数 (excel中为N_tower)
N_pump_station_count = data1(6); % 泵站数 (excel中为N_pump)
N_source = N_tower_count + N_pump_station_count; % 水源数
M = MT - N_source;  % 不含水源的节点数
C_coeff = data1(7);   % 水头参数 (Hazen-Williams C)

% 提取管段和节点流量数据
NM_original = data2_original(:, 2:3); % 原始管段与上下游节点关联矩阵 NM N×2
LD = data2_original(:, 6);      % 管长
D_diameters = data2_original(:, 7); % 管径
% m_exponent = data2_original(1,8); % 摩阻指数 (似乎未使用 Hazen-Williams 时 m=1.852, Q的指数是0.54 -> 1/1.852)
Q_initial = data2_original(:, 9); % 管段初始流量m^3/s

QX = []; HX = []; SX_original = []; NQ_source_pipes = []; NX_pump_start_node = []; NX_pump_end_node = [];
if LX ~= 0
    QX = data3(:, 2);           % QX,节点流量m^3/s
    HX = data4(:, 2);           % 水源总水头 (对于泵，这可以看作是吸水井水位或固定吸水端压力)
    SX_original = data4(:, 3);       % 原始水源出水管虚阻耗系数(s^2/(m^3))
    NQ_source_pipes = data4(:, 4);   % 水源出水管管段编号
    NX_pump_start_node = data4(:,5); % 虚环泵站始端水源编号 (UNUSED in this version directly)
    NX_pump_end_node = data4(:,6);   % 虚环泵站末端水源编号 (UNUSED in this version directly)
end

EZ_tolerance = 0.001; % 回路水头闭合容差 (原为0.05，建议更小以保证精度)
STR_relaxation = 0.5; % 松弛因子

% --- 新增：定义要进行优化的两个水泵的参数 和 识别它们在data4中的信息 ---
% 假设我们要优化的两个泵是 data4 中的前两个泵站类型的水源
% 您需要根据实际情况调整 pump_indices_in_data4
pump_indices_in_data4 = [];
pump_count_identified = 0;
for k_source = 1:size(data4,1)
    % 假设泵站的SX值会比较特殊，或者您可以增加一列来标记泵站类型
    % 这里我们简单地假设前 N_pump_station_count 个是泵，并且我们要改前两个
    % 或者，如果 NQ_source_pipes 能唯一确定泵，也可以用它
    % 为了示例，我们假设 data4 中水源类型（比如通过SX值或一个新列）可以区分泵和塔
    % 此处需要用户根据其 Excel 表的具体情况来精确识别这两个泵
    % 例如，如果 pump_station_count 是 2，就取前两个类型为“泵”的水源
    % 为简单起见，这里假设前两个水源就是我们要改的泵 (需要用户确认！)
    if pump_count_identified < 2 % 我们只处理两个泵
        % 检查是否为泵站 (例如，可以基于data4中一个额外的类型列，或通过SX值范围等)
        % 简单示例：假设SX值大于某个阈值的是泵，或者就是按顺序
        pump_indices_in_data4 = [pump_indices_in_data4; k_source];
        pump_count_identified = pump_count_identified + 1;
    end
end

if length(pump_indices_in_data4) < 2
    error('未能识别出至少两个泵进行优化，请检查 pump_indices_in_data4 的设置逻辑');
end

% 这两个特定泵的特性曲线参数 H = H0 - Sk * Q^2
pump_params = [
    % H0 (m), Sk (s^2/m^5)
    92, 29.167;  % 泵1的参数
    89, 117.0;   % 泵2的参数
];

% 获取这两个特定泵的管段编号
pipe_idx_pump1 = NQ_source_pipes(pump_indices_in_data4(1));
pipe_idx_pump2 = NQ_source_pipes(pump_indices_in_data4(2));

% 获取这两个泵的吸水端节点编号 (从原始NM矩阵和NQ确定)
% 假设NQ(pump_idx)对应的管段的上游节点NM(NQ(pump_idx),1)是水源节点
% 这个水源节点的编号在 E 和 HX 中对应。
% NM_original 中的节点编号是全局节点编号 (1 to MT)
% E 向量中 1:M 是普通节点, M+1:MT 是水源节点, HX 的索引对应水源节点列表的顺序
% 我们需要找到这两个泵水源节点在 HX 数组中的索引，以便获取其固定水头
% 直接使用这些行号从 HX 中获取吸水端固定水头
% HX 的顺序与 data4 的行顺序一致
if max(pump_indices_in_data4) > length(HX)
    error('pump_indices_in_data4 中的索引超出了 HX 数组的界限。');
end

H_in_pump1 = HX(pump_indices_in_data4(1)); % 泵1吸水端固定水头
H_in_pump2 = HX(pump_indices_in_data4(2)); % 泵2吸水端固定水头

fprintf('信息: 泵1 (管段 %d) 吸水端水头 H_in_pump1 = %.2f\n', pipe_idx_pump1, H_in_pump1);
fprintf('信息: 泵2 (管段 %d) 吸水端水头 H_in_pump2 = %.2f\n', pipe_idx_pump2, H_in_pump2);


% 定义可选的下游接入节点（例如，所有非水源节点 1:M）
possible_discharge_nodes = (1:M)'; % 列向量

% 生成所有可能的两个不同下游接入点的组合
combinations = nchoosek(possible_discharge_nodes, 2);
% 如果允许两个泵接入同一个节点 (通常不允许，但这里可以考虑)
% temp_combs = allcomb(possible_discharge_nodes, possible_discharge_nodes);
% combinations = temp_combs(temp_combs(:,1) ~= temp_combs(:,2), :); % 确保两个泵接入不同节点
if isempty(combinations) && length(possible_discharge_nodes) >=2
    % 如果 nchoosek 返回空，可能是因为 possible_discharge_nodes 不足2个
    % 但如果>=2，可能是其他问题，或者如果想允许 A-B 和 B-A，则用 allcomb 后处理
     all_pairs = [];
     for n1_idx = 1:length(possible_discharge_nodes)
        for n2_idx = 1:length(possible_discharge_nodes)
            if possible_discharge_nodes(n1_idx) ~= possible_discharge_nodes(n2_idx)
                all_pairs = [all_pairs; possible_discharge_nodes(n1_idx), possible_discharge_nodes(n2_idx)];
            end
        end
     end
     combinations = unique(sort(all_pairs,2),'rows'); % 取唯一组合，不考虑顺序
end


results_summary = []; % 用于存储结果: [node_p1, node_p2, Q_p1, H_p1, Q_p2, H_p2]

fprintf('开始计算不同水泵接入点的工况...\n');

% --- 主循环：遍历所有接入点组合 ---
for combo_idx = 1:size(combinations, 1)
    current_discharge_node_p1 = combinations(combo_idx, 1);
    current_discharge_node_p2 = combinations(combo_idx, 2);

    fprintf('计算组合: 泵1 -> 节点 %d, 泵2 -> 节点 %d\n', current_discharge_node_p1, current_discharge_node_p2);

    % 1. 动态修改管网拓扑 (NM矩阵)
    NM_current = NM_original; % 从原始NM开始
    NM_current(pipe_idx_pump1, 2) = current_discharge_node_p1; % 修改泵1的下游节点
    NM_current(pipe_idx_pump2, 2) = current_discharge_node_p2; % 修改泵2的下游节点

    % 2. 重新计算 MIS, MJS, MM 矩阵 (基于 NM_current)
    MIS_current = zeros(M, N); % 估算最大连接数，或动态调整列数
    MJS_current = zeros(M, N);
    MM_current = zeros(M, 1);
    max_conn_per_node = 0; % 用于确定 MIS/MJS 的实际列数

    for I_node = 1:M % 对非水源节点循环
        K_count = 0;
        for J_pipe = 1:N % 对管段循环
            if NM_current(J_pipe, 1) == I_node % 节点I是管段J的上游节点
                K_count = K_count + 1;
                MIS_current(I_node, K_count) = J_pipe;
                MJS_current(I_node, K_count) = NM_current(J_pipe, 2);
            end
            if NM_current(J_pipe, 2) == I_node % 节点I是管段J的下游节点
                K_count = K_count + 1;
                MIS_current(I_node, K_count) = -J_pipe;
                MJS_current(I_node, K_count) = NM_current(J_pipe, 1);
            end
        end
        MM_current(I_node) = K_count;
        if K_count > max_conn_per_node
            max_conn_per_node = K_count;
        end
    end
    % 调整MIS/MJS的列数以节省空间
    MIS_current = MIS_current(:, 1:max_conn_per_node);
    MJS_current = MJS_current(:, 1:max_conn_per_node);

    % --- 开始水力计算求解器 (核心逻辑来自原脚本) ---
    % 节点初始压头
    E_iter = ones(M, 1) * 70; % 非水源节点的初始压头设置为70
    if ~isempty(HX)
        for I_hx = 1:(MT - M) % MT-M 是水源节点的数量
            E_iter(M + I_hx) = HX(I_hx); % 水源节点的压头 (E中索引 M+1 到 MT)
        end
    end

    % 计算管段流量系数R (Hazen-Williams: Q = R * Hf^0.54, Hf = (Q/R)^(1/0.54) )
    % R = (0.27853 * C * D^2.63) / L^0.54 is for Hf = L * (Q / (0.27853*C*D^0.63))^1.852
    % So R_calc = 0.27853 * C_coeff * D_diameters.^2.63 ./ LD.^0.54;
    % Your original R calculation: R(I)=0.27853*C*D(I)^2.63/LD(I)^0.54
    % This R means: Q = R * (abs(ET)/L)^0.54 * sign(ET)  if ET is head diff per unit length.
    % Or if ET is total head diff: Q = R_modified * abs(ET)^0.54 * sign(ET)
    % where R_modified = (0.27853 * C_coeff * D_diameters.^2.63) / LD   <- This matches Q=k*S^0.54 where S is slope ET/L
    % Let's use the form Q = R_coeff * abs(HeadLoss)^0.54
    % The original code uses R(I)=.../LD(I)^0.54. This implies Q = R * abs(ET)^0.54
    % This is equivalent to HeadLoss_per_unit_length S_f = (Q / (0.27853 C D^0.63))^1.852
    % Total HeadLoss Hf = L * S_f. So Q = (0.27853 C D^0.63) * (Hf/L)^0.54
    % Q = (0.27853 C D^2.63 / L^0.54) * (Hf)^0.54. This R matches.
    R_coeffs = zeros(N,1);
    for I_pipe_R = 1:N
        if LD(I_pipe_R) > 0 && D_diameters(I_pipe_R) > 0
             R_coeffs(I_pipe_R) = (0.27853 * C_coeff * D_diameters(I_pipe_R)^2.63) / (LD(I_pipe_R)^0.54);
        else
            R_coeffs(I_pipe_R) = 1e-9; % Avoid division by zero, make it very resistive
        end
    end

    % 对于非我们特定优化的其他水源管段 (如水塔)，如果它们也用SX模型
    if N_source > 0 && ~isempty(NQ_source_pipes) && ~isempty(SX_original)
        for k_s = 1:length(NQ_source_pipes)
            pipe_s_idx = NQ_source_pipes(k_s);
            % 检查这个水源不是我们正在用特性曲线处理的两个泵
            is_handled_pump = (pipe_s_idx == pipe_idx_pump1 && k_s == pump_indices_in_data4(1)) || ...
                              (pipe_s_idx == pipe_idx_pump2 && k_s == pump_indices_in_data4(2));
            if ~is_handled_pump && SX_original(k_s) > 0
                R_coeffs(pipe_s_idx) = 1 / SX_original(k_s)^0.5; % Q = R * Hf^0.5 for these SX based sources
            elseif ~is_handled_pump && SX_original(k_s) <= 0
                R_coeffs(pipe_s_idx) = 1e9; % Effectively no flow or very high resistance
            end
        end
    end
    R_coeffs = R_coeffs'; % To row vector if needed, or keep as column
    
    Q_current = Q_initial; % Initialize flows
    if size(Q_current,1) == 1; Q_current = Q_current'; end % Ensure column vector

    max_iterations_KK = 100; % Increased iterations for potentially harder problems
    convergence_achieved_flag = false;

    for KK = 1:max_iterations_KK
        E_full_current_iter = zeros(MT,1); % E_iter only stores M, need full for NM indexing
        E_full_current_iter(1:M) = E_iter(1:M);
        if ~isempty(HX)
             E_full_current_iter(M+1:MT) = HX; % Assuming HX maps directly to E(M+1:MT)
        end

        % 1. 计算管段流量 Q_current
        for I_pipe = 1:N
            % 获取管段首末节点在 E_full_current_iter 中的实际压头
            node_start_global = NM_current(I_pipe, 1);
            node_end_global   = NM_current(I_pipe, 2);
            
            H_start_pipe = E_full_current_iter(node_start_global);
            H_end_pipe   = E_full_current_iter(node_end_global);
            ET_pipe      = H_start_pipe - H_end_pipe; % 从上游到下游的压头差

            % 判断是否为我们正在处理的两个特定泵
            is_pump1_pipe = (I_pipe == pipe_idx_pump1);
            is_pump2_pipe = (I_pipe == pipe_idx_pump2);

            if is_pump1_pipe
                H0_p = pump_params(1,1); Sk_p = pump_params(1,2);
                % ET_pipe is E_in - E_out for the pump (suction head - discharge head)
                % Q^2 = (ET_pump_effective + H0_p) / Sk_p
                % where ET_pump_effective is E_suction_actual - E_discharge_actual
                % Here, ET_pipe is already H_start(suction for pump) - H_end(discharge for pump)
                arg_sqrt = (ET_pipe + H0_p) / Sk_p;
                if arg_sqrt >= 1e-9 % Allow small negative due to numerics, treat as zero flow
                    Q_current(I_pipe) = sqrt(arg_sqrt);
                else
                    Q_current(I_pipe) = 0; % No flow or reverse flow not handled by this pump model
                end

            elseif is_pump2_pipe
                H0_p = pump_params(2,1); Sk_p = pump_params(2,2);
                arg_sqrt = (ET_pipe + H0_p) / Sk_p;
                if arg_sqrt >= 1e-9
                    Q_current(I_pipe) = sqrt(arg_sqrt);
                else
                    Q_current(I_pipe) = 0;
                end
            else % 其他管段或使用SX系数的水源 (如水塔)
                % 查找该管段是否为其他SX水源管段
                is_other_sx_source = false;
                sx_source_k_idx = -1;
                if ~isempty(NQ_source_pipes)
                    found_other_sx = find(NQ_source_pipes == I_pipe, 1);
                    if ~isempty(found_other_sx)
                        % Ensure it's not pump1 or pump2's original SX entry we're overriding
                        if ~( (I_pipe == pipe_idx_pump1 && found_other_sx == pump_indices_in_data4(1)) || ...
                              (I_pipe == pipe_idx_pump2 && found_other_sx == pump_indices_in_data4(2)) )
                            is_other_sx_source = true;
                            sx_source_k_idx = found_other_sx; % index in data4 / SX_original
                        end
                    end
                end

                if is_other_sx_source && SX_original(sx_source_k_idx) > 0 % SX based source (Q = R*sqrt(H))
                    % R_coeffs(I_pipe) for SX sources was 1/sqrt(SX)
                    % Q = (1/sqrt(SX)) * sqrt(abs(ET_pipe)) * sign(ET_pipe) - check flow direction
                    % Typically, source flow direction is fixed or ET_pipe is H_source - H_network_node
                    Q_current(I_pipe) = R_coeffs(I_pipe) * sqrt(abs(ET_pipe)) * sign(ET_pipe); % Needs care for sign convention
                                                                                            % For source ET is usually H_source - H_node
                else % 普通阻力管段 (Hazen-Williams)
                    if abs(ET_pipe) < 1e-7 % Avoid division by zero if ET is very small
                        Q_current(I_pipe) = 0;
                    else
                        Q_current(I_pipe) = R_coeffs(I_pipe) * (abs(ET_pipe)^0.54) * sign(ET_pipe);
                    end
                end
            end
        end % End loop for Q calculation

        % 2. 计算节点流量不平衡量 EQ_imbalance
        EQ_imbalance = zeros(M,1);
        for I_node_bal = 1:M
            EQ_imbalance(I_node_bal) = QX(I_node_bal); % 外部流量
            for J_conn = 1:MM_current(I_node_bal)
                pipe_id_bal = abs(MIS_current(I_node_bal, J_conn));
                flow_direction_sign = sign(MIS_current(I_node_bal, J_conn)); % -1 if I_node is downstream, +1 if upstream
                % EQ_imbalance = QX - sum(Q_out) + sum(Q_in)
                % If MIS is positive, pipe J_pipe (ID) starts at I_node, so Q(pipe_id) is outgoing. term is -Q.
                % If MIS is negative, pipe J_pipe (ID) ends at I_node, so Q(pipe_id) is incoming. term is +Q.
                % Original: EQ(I)=EQ(I)+Q(abs(T))*sign(T); where T=MIS.
                % This means QX + sum(Q_ending_at_I) - sum(Q_starting_at_I)
                % Let's use: QX_i - sum_{k_out} Q_k_out + sum_{k_in} Q_k_in = 0
                % If MIS is positive, flow Q(pipe_id) is OUT from I_node. EQ = EQ - Q.
                % If MIS is negative, flow Q(pipe_id) is IN to I_node. EQ = EQ + Q.
                % Original code's sign(T) logic:
                % if T positive (I is upstream of pipe T), sign(T)=+1. Q(abs(T)) is flow from I.
                % EQ_imbalance(I_node_bal) = EQ_imbalance(I_node_bal) + Q_current(pipe_id_bal) * flow_direction_sign;
                % This should be: EQ_imbalance(I_node_bal) = EQ_imbalance(I_node_bal) - Q_current(pipe_id_bal) * flow_direction_sign;
                % (Assuming Q_current is positive if flowing from NM(:,1) to NM(:,2))
                % Let's re-verify the original: EQ(I) = QX(I); for J=1:MM(I) T=MIS(I,J); EQ(I)=EQ(I)+Q(abs(T))*sign(T); end
                % If T > 0, pipe abs(T) starts at I. sign(T) = 1. Q is flow value. EQ = QX + Q. This means QX + Q_out = 0. This is wrong.
                % It should be QX - Q_out_from_I + Q_in_to_I = 0.
                % If MIS(I,J_conn) > 0, node I is upstream of pipe_id_bal. Flow Q_current(pipe_id_bal) is leaving I. So, subtract.
                % If MIS(I,J_conn) < 0, node I is downstream of pipe_id_bal. Flow Q_current(pipe_id_bal) is entering I. So, add.
                 EQ_imbalance(I_node_bal) = EQ_imbalance(I_node_bal) - (Q_current(pipe_id_bal) * flow_direction_sign);
            end
        end
        
        % 3. 计算雅可比矩阵系数 QJ_derivatives (dQ/d(ET_pipe))
        QJ_derivatives = zeros(N,1);
        for I_pipe_qj = 1:N
            node_start_global_qj = NM_current(I_pipe_qj, 1);
            node_end_global_qj   = NM_current(I_pipe_qj, 2);
            H_start_pipe_qj = E_full_current_iter(node_start_global_qj);
            H_end_pipe_qj   = E_full_current_iter(node_end_global_qj);
            ET_pipe_qj      = H_start_pipe_qj - H_end_pipe_qj;

            is_pump1_pipe_qj = (I_pipe_qj == pipe_idx_pump1);
            is_pump2_pipe_qj = (I_pipe_qj == pipe_idx_pump2);

            if is_pump1_pipe_qj
                Sk_p_qj = pump_params(1,2);
                if abs(Q_current(I_pipe_qj)) < 1e-6 % If flow is very small
                    QJ_derivatives(I_pipe_qj) = 1e9; % Large derivative for stability (or based on H0/Sk)
                else
                    QJ_derivatives(I_pipe_qj) = 1 / (2 * Sk_p_qj * Q_current(I_pipe_qj));
                end
            elseif is_pump2_pipe_qj
                Sk_p_qj = pump_params(2,2);
                if abs(Q_current(I_pipe_qj)) < 1e-6
                    QJ_derivatives(I_pipe_qj) = 1e9;
                else
                    QJ_derivatives(I_pipe_qj) = 1 / (2 * Sk_p_qj * Q_current(I_pipe_qj));
                end
            else % Other pipes
                 is_other_sx_source_qj = false; sx_source_k_idx_qj = -1;
                 if ~isempty(NQ_source_pipes)
                    found_other_sx_qj = find(NQ_source_pipes == I_pipe_qj, 1);
                    if ~isempty(found_other_sx_qj)
                        if ~( (I_pipe_qj == pipe_idx_pump1 && found_other_sx_qj == pump_indices_in_data4(1)) || ...
                              (I_pipe_qj == pipe_idx_pump2 && found_other_sx_qj == pump_indices_in_data4(2)) )
                            is_other_sx_source_qj = true;
                            sx_source_k_idx_qj = found_other_sx_qj;
                        end
                    end
                 end

                if is_other_sx_source_qj && SX_original(sx_source_k_idx_qj) > 0 % SX based source Q = R * Hf^0.5
                    if abs(ET_pipe_qj) < 1e-7
                        QJ_derivatives(I_pipe_qj) = R_coeffs(I_pipe_qj) * 0.5 / (1e-7)^0.5; % Avoid div by zero
                    else
                        QJ_derivatives(I_pipe_qj) = R_coeffs(I_pipe_qj) * 0.5 / sqrt(abs(ET_pipe_qj));
                    end
                else % Regular Hazen-Williams pipe Q = R * Hf^0.54
                    if abs(ET_pipe_qj) < 1e-7
                         %QJ_derivatives(I_pipe_qj) = R_coeffs(I_pipe_qj) * 0.54 / (1e-7)^0.46;
                         QJ_derivatives(I_pipe_qj) = 1e9; % Or a large number if ET is tiny
                    else
                        QJ_derivatives(I_pipe_qj) = R_coeffs(I_pipe_qj) * 0.54 / (abs(ET_pipe_qj)^0.46);
                    end
                end
            end
             if QJ_derivatives(I_pipe_qj) == 0 || isnan(QJ_derivatives(I_pipe_qj)) || isinf(QJ_derivatives(I_pipe_qj))
                QJ_derivatives(I_pipe_qj) = 1e9; % Stabilize if derivative is problematic
            end
        end % End QJ calculation

        % 4. 构建雅可比矩阵 AJ_jacobian
        AJ_jacobian = zeros(M,M);
        for I_node_jac = 1:M % For each equation (node)
            for J_conn_jac = 1:MM_current(I_node_jac) % For each pipe connected to this node
                pipe_id_jac = abs(MIS_current(I_node_jac, J_conn_jac));
                other_node_global_idx = MJS_current(I_node_jac, J_conn_jac); % Global ID of the other node of this pipe

                % Diagonal element: d(EQ_I)/d(E_I)
                AJ_jacobian(I_node_jac, I_node_jac) = AJ_jacobian(I_node_jac, I_node_jac) + QJ_derivatives(pipe_id_jac);
                
                % Off-diagonal element: d(EQ_I)/d(E_K) where K is other_node
                if other_node_global_idx <= M % If the other node is also a non-source node
                    AJ_jacobian(I_node_jac, other_node_global_idx) = AJ_jacobian(I_node_jac, other_node_global_idx) - QJ_derivatives(pipe_id_jac);
                end
            end
            if abs(AJ_jacobian(I_node_jac, I_node_jac)) < 1e-9 % Prevent singular matrix if diagonal is zero
                 AJ_jacobian(I_node_jac, I_node_jac) = 1e-9 * sign(AJ_jacobian(I_node_jac, I_node_jac));
                 if AJ_jacobian(I_node_jac,I_node_jac) == 0, AJ_jacobian(I_node_jac,I_node_jac) = 1e-9; end
            end
        end

        % 5. 求解水头修正量 DE_head_correction
        if rcond(AJ_jacobian) < 1e-15 % Check condition number
            fprintf('警告: 雅可比矩阵奇异或接近奇异 (组合 %d, 迭代 %d)。跳过此组合。\n', combo_idx, KK);
            convergence_achieved_flag = false; % Mark as not converged
            break; % Exit KK loop for this combination
        end
        DE_head_correction = AJ_jacobian \ EQ_imbalance; % Solve for head corrections

        % 6. 更新节点水头 E_iter (only for non-source nodes 1:M)
        E_iter(1:M) = E_iter(1:M) - STR_relaxation * DE_head_correction(1:M);

        % 检查收敛性
        max_imbalance = max(abs(EQ_imbalance));
        if max_imbalance < EZ_tolerance
            convergence_achieved_flag = true;
            % fprintf('组合 %d 在 %d 次迭代后收敛。最大流量不平衡: %e\n', combo_idx, KK, max_imbalance);
            break; % Exit KK loop
        end
         if KK == max_iterations_KK
            fprintf('警告: 组合 %d, 水力计算达到最大迭代次数 %d, 未完全收敛。最大流量不平衡: %e\n', combo_idx, max_iterations_KK, max_imbalance);
        end
    end % End KK solver loop

    % --- 存储当前组合的结果 ---
    if convergence_achieved_flag
        Q_op1 = Q_current(pipe_idx_pump1);
        H_op1 = pump_params(1,1) - pump_params(1,2) * Q_op1^2;
        % 也可以用 E_out - E_in, 即 E_iter(current_discharge_node_p1) - H_in_pump1 (注意符号，泵扬程是正)
        % H_op1_check = E_iter(current_discharge_node_p1) - H_in_pump1;

        Q_op2 = Q_current(pipe_idx_pump2);
        H_op2 = pump_params(2,1) - pump_params(2,2) * Q_op2^2;
        % H_op2_check = E_iter(current_discharge_node_p2) - H_in_pump2;

        results_summary = [results_summary; current_discharge_node_p1, current_discharge_node_p2, Q_op1, H_op1, Q_op2, H_op2];
        fprintf('  结果: Q1=%.4f, H1=%.2f; Q2=%.4f, H2=%.2f\n', Q_op1, H_op1, Q_op2, H_op2);

        % (可选) 打印更详细的管网结果
        % disp('***管段流量***'); for I=1:N, fprintf('%s %s %d %s %f\n','Q','(',I,')=',Q_current(I)); end
        % disp('***节点压头***');
        % E_final_display = zeros(MT,1); E_final_display(1:M)=E_iter(1:M);
        % if ~isempty(HX); E_final_display(M+1:MT)=HX; end
        % for I=1:MT, fprintf('%s %s %d %s %f\n','E','(',I,')=',E_final_display(I)); end

    else
        results_summary = [results_summary; current_discharge_node_p1, current_discharge_node_p2, NaN, NaN, NaN, NaN];
        fprintf('  结果: 此组合未收敛或计算失败。\n');
    end

end % End main loop for combinations

% --- 显示最终总结结果 ---
disp('----------------------------------------------------------');
disp('所有水泵接入点组合的计算结果总结:');
disp('泵1接入点 | 泵2接入点 | 泵1流量(Q1) | 泵1扬程(H1) | 泵2流量(Q2) | 泵2扬程(H2)');
disp('----------------------------------------------------------');
if ~isempty(results_summary)
    for i = 1:size(results_summary,1)
        fprintf('%10d | %10d | %12.4f | %12.2f | %12.4f | %12.2f\n', ...
            results_summary(i,1), results_summary(i,2), ...
            results_summary(i,3), results_summary(i,4), ...
            results_summary(i,5), results_summary(i,6));
    end
else
    disp('没有计算得到任何有效结果。');
end
disp('----------------------------------------------------------');

%% 辅助函数 (如果需要 allcomb)
% function C = allcomb(varargin)
%     args = varargin;
%     n = nargin;
%     [F{1:n}] = ndgrid(args{:});
%     for k_ac = n:-1:1
%         G_ac(:,k_ac) = F{k_ac}(:);
%     end
%     C = unique(G_ac, 'rows');
% end
